import { ref, reactive } from 'vue'
import { radiationState } from './dataStore.js'
import { currentLocation } from './locationService.js'

// 报警状态
export const alertState = reactive({
  isEnabled: true,
  soundEnabled: true,
  vibrationEnabled: true,
  visualEnabled: true,
  lastAlertTime: null,
  alertCooldown: 30000, // 30秒冷却时间，避免频繁报警
  alertHistory: []
})

// 报警级别配置
export const alertLevels = {
  info: {
    name: '信息',
    color: '#3b82f6',
    priority: 1,
    sound: false,
    vibration: false
  },
  warning: {
    name: '警告',
    color: '#f59e0b',
    priority: 2,
    sound: true,
    vibration: true
  },
  danger: {
    name: '危险',
    color: '#ef4444',
    priority: 3,
    sound: true,
    vibration: true
  },
  critical: {
    name: '严重',
    color: '#dc2626',
    priority: 4,
    sound: true,
    vibration: true
  }
}

// 当前活跃的报警
export const activeAlerts = ref([])

// 地图上的报警标记
export const alertMarkers = ref([])

/**
 * 检查辐射阈值
 */
export const checkRadiationThreshold = () => {
  const { currentData, settings } = radiationState
  const { doseRate, doseSum, temperature, cps } = currentData
  const { maxDoseRate, minDoseRate, maxDoseSum } = settings

  const alerts = []

  // 检查剂量率过高
  if (doseRate > maxDoseRate) {
    const level = doseRate > maxDoseRate * 1.5 ? 'critical' : 'danger'
    alerts.push({
      type: 'high_dose_rate',
      level,
      message: `剂量率过高: ${doseRate.toFixed(3)} μSv/h`,
      value: doseRate,
      threshold: maxDoseRate,
      location: getCurrentLocationData()
    })
  }

  // 检查剂量率异常低
  if (doseRate < minDoseRate && doseRate > 0) {
    alerts.push({
      type: 'low_dose_rate',
      level: 'warning',
      message: `剂量率异常低: ${doseRate.toFixed(3)} μSv/h`,
      value: doseRate,
      threshold: minDoseRate,
      location: getCurrentLocationData()
    })
  }

  // 检查累积剂量过高
  if (doseSum > maxDoseSum) {
    const level = doseSum > maxDoseSum * 1.5 ? 'critical' : 'danger'
    alerts.push({
      type: 'high_dose_sum',
      level,
      message: `累积剂量过高: ${doseSum.toFixed(3)} μSv`,
      value: doseSum,
      threshold: maxDoseSum,
      location: getCurrentLocationData()
    })
  }

  // 检查温度异常
  if (temperature > 50 || temperature < -20) {
    alerts.push({
      type: 'temperature_abnormal',
      level: 'warning',
      message: `环境温度异常: ${temperature.toFixed(1)}°C`,
      value: temperature,
      threshold: temperature > 50 ? 50 : -20,
      location: getCurrentLocationData()
    })
  }

  // 检查计数率异常
  if (cps > 1000) {
    alerts.push({
      type: 'high_cps',
      level: 'warning',
      message: `计数率异常: ${cps} CPS`,
      value: cps,
      threshold: 1000,
      location: getCurrentLocationData()
    })
  }

  return alerts
}

/**
 * 获取当前位置数据
 */
const getCurrentLocationData = () => {
  return {
    latitude: currentLocation.value.latitude,
    longitude: currentLocation.value.longitude,
    address: currentLocation.value.address || '未知位置',
    timestamp: Date.now()
  }
}

/**
 * 触发报警
 */
export const triggerAlert = (alert) => {
  if (!alertState.isEnabled) return

  // 检查冷却时间
  const now = Date.now()
  if (alertState.lastAlertTime && (now - alertState.lastAlertTime) < alertState.alertCooldown) {
    return
  }

  const alertLevel = alertLevels[alert.level]
  if (!alertLevel) return

  // 添加到活跃报警列表
  const alertId = `${alert.type}_${now}`
  const fullAlert = {
    id: alertId,
    ...alert,
    timestamp: now,
    acknowledged: false
  }

  activeAlerts.value.push(fullAlert)

  // 添加到历史记录
  alertState.alertHistory.unshift(fullAlert)
  if (alertState.alertHistory.length > 100) {
    alertState.alertHistory = alertState.alertHistory.slice(0, 100)
  }

  // 添加地图标记
  if (alert.location && alert.location.latitude && alert.location.longitude) {
    addAlertMarker(fullAlert)
  }

  // 触发各种报警方式
  if (alertState.visualEnabled) {
    showVisualAlert(fullAlert)
  }

  if (alertState.soundEnabled && alertLevel.sound) {
    playAlertSound(alert.level)
  }

  if (alertState.vibrationEnabled && alertLevel.vibration) {
    triggerVibration(alert.level)
  }

  alertState.lastAlertTime = now
}

/**
 * 显示视觉报警
 */
const showVisualAlert = (alert) => {
  const alertLevel = alertLevels[alert.level]
  
  uni.showModal({
    title: `${alertLevel.name}报警`,
    content: alert.message,
    showCancel: true,
    cancelText: '忽略',
    confirmText: '查看',
    confirmColor: alertLevel.color,
    success: (res) => {
      if (res.confirm) {
        // 跳转到地图页面查看位置
        if (alert.location) {
          uni.navigateTo({
            url: '/pages/map/map'
          })
        }
      }
      acknowledgeAlert(alert.id)
    }
  })
}

/**
 * 播放报警声音
 */
const playAlertSound = (level) => {
  // uni-app中可以使用音频API播放声音
  // 这里简化处理，使用系统提示音
  if (level === 'critical' || level === 'danger') {
    // 播放多次提示音表示严重程度
    for (let i = 0; i < 3; i++) {
      setTimeout(() => {
        uni.showToast({
          title: '⚠️',
          icon: 'none',
          duration: 500
        })
      }, i * 600)
    }
  } else {
    uni.showToast({
      title: '⚠️',
      icon: 'none',
      duration: 500
    })
  }
}

/**
 * 触发震动
 */
const triggerVibration = (level) => {
  if (level === 'critical' || level === 'danger') {
    // 长震动表示严重程度
    uni.vibrateLong()
  } else {
    // 短震动
    uni.vibrateShort()
  }
}

/**
 * 添加报警标记到地图
 */
const addAlertMarker = (alert) => {
  const marker = {
    id: alert.id,
    latitude: alert.location.latitude,
    longitude: alert.location.longitude,
    iconPath: `/static/icons/alert-${alert.level}.png`,
    width: 35,
    height: 35,
    title: alert.message,
    level: alert.level,
    timestamp: alert.timestamp,
    callout: {
      content: `⚠️ ${alert.message}`,
      color: alertLevels[alert.level].color,
      fontSize: 12,
      borderRadius: 4,
      bgColor: '#fff',
      padding: 5,
      display: 'BYCLICK'
    }
  }

  alertMarkers.value.push(marker)

  // 限制标记数量
  if (alertMarkers.value.length > 50) {
    alertMarkers.value = alertMarkers.value.slice(-50)
  }
}

/**
 * 确认报警
 */
export const acknowledgeAlert = (alertId) => {
  const alert = activeAlerts.value.find(a => a.id === alertId)
  if (alert) {
    alert.acknowledged = true
    // 从活跃列表中移除
    activeAlerts.value = activeAlerts.value.filter(a => a.id !== alertId)
  }
}

/**
 * 清除所有报警
 */
export const clearAllAlerts = () => {
  activeAlerts.value = []
  alertMarkers.value = []
}

/**
 * 清除过期的报警标记
 */
export const clearExpiredAlerts = (maxAge = 3600000) => { // 默认1小时
  const now = Date.now()
  alertMarkers.value = alertMarkers.value.filter(marker => 
    (now - marker.timestamp) < maxAge
  )
}

/**
 * 开始监控
 */
export const startAlertMonitoring = () => {
  const checkInterval = setInterval(() => {
    const alerts = checkRadiationThreshold()
    alerts.forEach(alert => {
      triggerAlert(alert)
    })
    
    // 清除过期报警
    clearExpiredAlerts()
  }, 5000) // 每5秒检查一次

  return checkInterval
}

/**
 * 停止监控
 */
export const stopAlertMonitoring = (intervalId) => {
  if (intervalId) {
    clearInterval(intervalId)
  }
}

/**
 * 获取报警统计
 */
export const getAlertStats = () => {
  const stats = {
    total: alertState.alertHistory.length,
    byLevel: {},
    byType: {},
    recent: alertState.alertHistory.slice(0, 10)
  }

  alertState.alertHistory.forEach(alert => {
    stats.byLevel[alert.level] = (stats.byLevel[alert.level] || 0) + 1
    stats.byType[alert.type] = (stats.byType[alert.type] || 0) + 1
  })

  return stats
}
